<div :if={Phoenix.Flash.get(@flash, :error)} class="alert alert-error mb-2 mx-2 mt-2 fixed top-4 right-4 z-[100] w-80 sm:w-96 max-w-80 sm:max-w-96 shadow-lg">
  <.icon name="hero-exclamation-circle" class="size-5" />
  <span>{Phoenix.Flash.get(@flash, :error)}</span>
  <button
    type="button"
    class="btn btn-sm btn-ghost"
    phx-click="lv:clear-flash"
    phx-value-key="error"
  >
    ×
  </button>
</div>

<!-- Unified Panel for all components -->
<div class={[
  "unified-panel bg-base-100 flex",
  if(@active_connection_set && Map.get(@expanded_sets, @active_connection_set.name, "expanded") == "collapsed", do: "broker-tabs-collapsed", else: "")
]}>
  <!-- Icon Menu -->
  <.icon_menu class="panel-icon-menu">
    <:top_item icon="hero-inbox-stack" tooltip="Brokers" active={true} navigate={~p"/"} />
    <:top_item icon="hero-wrench-screwdriver" href="#toolbox" tooltip="Toolbox" />

    <:bottom_item icon="hero-cog-6-tooth" href="#settings" tooltip="Settings" />

    <:bottom_item icon="hero-user-circle" tooltip="Account" />
  </.icon_menu>



  <!-- Main Content Area -->
  <div class="flex-1 flex flex-col overflow-y-auto">
    <!-- Broker Tabs -->
    <.live_component
      module={MqttableWeb.BrokerTabsComponent}
      id="broker-tabs"
      connection_sets={@connection_sets}
      active_connection_set={@active_connection_set}
      expanded_sets={@expanded_sets}
    />

        <!-- Connection Component -->
    <div class="border-t border-base-300">
      <.live_component
        module={MqttableWeb.ConnectionComponent}
        id="connection-component"
        active_connection_set={@active_connection_set}
        expanded_sets={@expanded_sets}
      />
    </div>
     <!-- Trace Component -->
    <div class="border-t border-base-300">
      <.live_component
        module={MqttableWeb.TraceComponent}
        id="trace-component"
        trace_messages={@trace_messages}
        active_broker_name={if @active_connection_set, do: @active_connection_set.name, else: nil}
      />
    </div>
  </div>

  <!-- Floating Action Button -->
  <button
    class="btn btn-primary btn-circle btn-lg absolute bottom-4 right-4 z-50 shadow-lg hover:shadow-xl transition-shadow"
    phx-click="open_send_modal"
    title="Send MQTT Message"
  >
    <.icon name="hero-paper-airplane" class="size-6" />
  </button>
</div>

  <%= if @show_modal do %>
    <.modal id="connection-set-modal" show on_cancel={JS.push("close_modal")}>
      <%= case @modal_type do %>
        <% :new_connection_set -> %>
          <.live_component
            module={MqttableWeb.NewConnectionSetModalComponent}
            id="new-connection-set-modal"
            edit_connection_set={@edit_connection_set}
            connection_sets={@connection_sets}
          />
        <% :edit_connection_set -> %>
          <.live_component
            module={MqttableWeb.EditConnectionSetModalComponent}
            id="edit-connection-set-modal"
            edit_connection_set={@edit_connection_set}
            connection_sets={@connection_sets}
          />
        <% :edit_connection -> %>
          <.live_component
            module={MqttableWeb.EditConnectionModalComponent}
            id="edit-connection-modal"
            edit_connection={@edit_connection}
            connection_set={@connection_set}
          />
        <% :new_connection -> %>
          <.live_component
            module={MqttableWeb.NewConnectionModalComponent}
            id="new-connection-modal"
            connection_set={@connection_set}
            edit_connection={@edit_connection}
          />
      <% end %>
    </.modal>
  <% end %>

  <%= if @show_subscription_modal do %>
    <.live_component
      module={MqttableWeb.SubscriptionModalComponent}
      id="subscription-modal"
      active_connection_set={@active_connection_set}
      pre_selected_client_id={@pre_selected_client_id}
      edit_mode={@edit_mode}
      client_id={@client_id}
      topic={@topic}
      qos={@qos}
      nl={@nl}
      rap={@rap}
      rh={@rh}
      sub_id={@sub_id}
      index={@index}
    />
  <% end %>

  <!-- Send Message Modal Component -->
  <.live_component
    module={MqttableWeb.SendMessageModalComponent}
    id="send-message-modal"
    show_modal={@show_send_modal}
    active_broker_name={if @active_connection_set, do: @active_connection_set.name, else: nil}
    form_state={@send_modal_form_state}
  />

  <!-- Message Detail Modal Component -->
  <.live_component
    module={MqttableWeb.MessageDetailModalComponent}
    id="message-detail-modal"
    show_modal={@show_detail_modal}
    message={@detail_modal_message}
    payload_view_type={@payload_view_type}
  />

