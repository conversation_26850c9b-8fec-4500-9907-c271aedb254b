defmodule MqttableWeb.ConnectionSets.Manager do
  @moduledoc """
  Module for managing connection sets.
  This module provides functions for creating, updating, and deleting connection sets.
  """

  import Phoenix.Component
  import Phoenix.LiveView

  # Import verified routes
  use Phoenix.VerifiedRoutes,
    endpoint: MqttableWeb.Endpoint,
    router: MqttableWeb.Router,
    statics: MqttableWeb.static_paths()

  alias Mqttable.ConnectionSets
  alias MqttableWeb.Utils.ConnectionHelpers

  @doc """
  Handles the open_connection_set_modal event.
  """
  def handle_open_connection_set_modal(socket) do
    # Create a new connection set with an empty variable row
    variables = [%{name: "", value: "", enabled: true}]

    # Create a new connection set template
    new_set = %{
      protocol: "mqtt",
      host: "",
      port: "1883",
      color: "blue",
      variables: variables,
      ssl_enabled: false,
      alpn: "",
      certificate_type: "ca_signed",
      ca_file: "",
      client_cert_file: "",
      client_key_file: ""
    }

    socket =
      socket
      |> assign(:show_modal, true)
      |> assign(:modal_type, :new_connection_set)
      |> assign(:edit_connection_set, new_set)

    # Schedule focus on the host field after the modal is rendered
    # Process.send_after(self(), {:focus_element, "connection-set-host"}, 100)

    {:noreply, socket}
  end

  @doc """
  Handles the open_edit_connection_set_modal event.
  """
  def handle_open_edit_connection_set_modal(socket, %{"name" => name}) do
    set = ConnectionHelpers.find_connection_set_by_name(socket.assigns.connection_sets, name)

    if set == nil do
      # If connection set not found, just close the modal
      {:noreply,
       socket
       |> assign(:show_modal, false)
       |> assign(:modal_type, nil)
       |> assign(:edit_connection_set, nil)}
    else
      # Ensure the connection set has a variables field
      set = if Map.has_key?(set, :variables), do: set, else: Map.put(set, :variables, [])

      # No longer automatically add an empty row
      set_with_empty_row = set

      # Find the index of the last empty row
      last_empty_index =
        Enum.find_index(Enum.reverse(set_with_empty_row.variables), fn var -> var.name == "" end)

      last_index =
        if last_empty_index do
          length(set_with_empty_row.variables) - 1 - last_empty_index
        else
          nil
        end

      socket =
        socket
        |> assign(:show_modal, true)
        |> assign(:modal_type, :edit_connection_set)
        |> assign(:edit_connection_set, set_with_empty_row)
        |> assign(:active_connection_set, set)

      # Schedule focus on the last empty field after the modal is rendered
      if last_index do
        Process.send_after(self(), {:focus_element, "var-name-#{last_index}"}, 100)
      end

      {:noreply, socket}
    end
  end

  @doc """
  Handles the save_connection_set event.
  """
  def handle_save_connection_set(socket, %{"connection_set" => set_params} = params) do
    protocol = set_params["protocol"] || "mqtt"
    host = set_params["host"]
    port = set_params["port"] || "1883"

    # File uploads are now handled in the component

    # Ensure host is not empty
    if host && host != "" do
      # Process variables from the form and filter out empty ones
      variables =
        if Map.has_key?(params, "variable") do
          Enum.map(params["variable"], fn {_index, var} ->
            %{
              name: var["name"],
              value: var["value"] || "",
              enabled: var["enabled"] == "on"
            }
          end)
          |> Enum.filter(fn var -> var.name != "" end)
        else
          []
        end

      # Use the provided name from the form or generate one if empty
      name =
        if set_params["name"] && set_params["name"] != "" do
          set_params["name"]
        else
          "#{protocol}_#{host}_#{port}" |> String.replace(".", "_")
        end

      # Check if a connection set with the same name already exists
      name_exists = Enum.any?(socket.assigns.connection_sets, fn set -> set.name == name end)

      if name_exists do
        # If a connection set with the same name already exists, return an error
        {:noreply,
         socket
         |> put_flash(:error, "A broker with the name '#{name}' already exists")
         |> assign(
           :edit_connection_set,
           Map.put(socket.assigns.edit_connection_set, :name_error, "This name is already in use")
         )}
      else
        # Create a new connection set with the variables and empty connections
        new_set = %{
          name: name,
          protocol: protocol,
          host: host,
          port: port,
          variables: variables,
          connections: [],
          ssl_enabled: set_params["ssl_enabled"] == "on",
          alpn: set_params["alpn"] || "",
          certificate_type: set_params["certificate_type"] || "ca_signed",
          ca_file: set_params["ca_file"] || "",
          client_cert_file: set_params["client_cert_file"] || "",
          client_key_file: set_params["client_key_file"] || ""
        }

        # Add the new connection set to the list
        connection_sets = socket.assigns.connection_sets ++ [new_set]

        # Save the updated connection sets to persistent storage
        save_connection_set(connection_sets)

        # Load trace messages for the new broker
        trace_messages = Mqttable.TraceManager.get_messages(new_set.name)

        # Mark this change as initiated locally to prevent loops
        socket = assign(socket, :active_set_change_source, "local")

        # Update the active connection set in the UI state to broadcast to all clients
        ConnectionSets.update_active_connection_set(new_set.name)

        # Set the new broker as the active broker
        {:noreply,
         socket
         |> assign(:connection_sets, connection_sets)
         |> assign(:active_connection_set, new_set)
         |> assign(:trace_messages, trace_messages)
         |> assign(:show_modal, false)
         |> assign(:edit_connection_set, nil)}
      end
    else
      # If host is empty, return without doing anything
      {:noreply, socket}
    end
  end

  @doc """
  Handles the update_connection_set event with variables.
  """
  def handle_update_connection_set(
        socket,
        %{"connection_set" => set_params, "old_name" => old_name, "variable" => var_params}
      ) do
    edit_set = socket.assigns.edit_connection_set
    protocol = set_params["protocol"] || Map.get(edit_set, :protocol, "mqtt")
    host = set_params["host"]
    port = set_params["port"] || Map.get(edit_set, :port, "1883")

    # File uploads are now handled in the component

    # Ensure host is not empty
    if host && host != "" do
      # Process variables from the form and filter out empty ones
      updated_vars =
        Enum.map(var_params, fn {_index, var} ->
          %{
            name: var["name"],
            value: var["value"] || "",
            enabled: var["enabled"] == "on"
          }
        end)
        |> Enum.filter(fn var -> var.name != "" end)

      # Use the provided name from the form or generate one if empty
      name =
        if set_params["name"] && set_params["name"] != "" do
          set_params["name"]
        else
          "#{protocol}_#{host}_#{port}" |> String.replace(".", "_")
        end

      # Check if a connection set with the same name already exists (excluding the current one being edited)
      name_exists =
        Enum.any?(socket.assigns.connection_sets, fn set ->
          set.name == name && set.name != old_name
        end)

      if name_exists do
        # If a connection set with the same name already exists, return an error
        {:noreply,
         socket
         |> put_flash(:error, "A broker with the name '#{name}' already exists")
         |> assign(
           :edit_connection_set,
           Map.put(edit_set, :name_error, "This name is already in use")
         )}
      else
        updated_set = %{
          name: name,
          protocol: protocol,
          host: host,
          port: port,
          variables: updated_vars,
          connections: Map.get(edit_set, :connections, []),
          ssl_enabled: set_params["ssl_enabled"] == "on",
          alpn: set_params["alpn"] || "",
          certificate_type: set_params["certificate_type"] || "ca_signed",
          ca_file: set_params["ca_file"] || "",
          client_cert_file: set_params["client_cert_file"] || "",
          client_key_file: set_params["client_key_file"] || ""
        }

        connection_sets =
          Enum.map(socket.assigns.connection_sets, fn set ->
            if set.name == old_name, do: updated_set, else: set
          end)

        # Save the updated connection sets to persistent storage
        save_connection_set(connection_sets)

        # Use the updated set directly as the active connection set
        active_connection_set = updated_set

        {:noreply,
         socket
         |> assign(:connection_sets, connection_sets)
         |> assign(:active_connection_set, active_connection_set)
         |> assign(:show_modal, false)
         |> assign(:edit_connection_set, nil)}
      end
    else
      # If host is empty, return without doing anything
      {:noreply, socket}
    end
  end

  # Handles the update_connection_set event without variables.
  def handle_update_connection_set(
        socket,
        %{"connection_set" => set_params, "old_name" => old_name}
      ) do
    # Handle case when no variables are present
    edit_set = socket.assigns.edit_connection_set
    protocol = set_params["protocol"] || Map.get(edit_set, :protocol, "mqtt")
    host = set_params["host"]
    port = set_params["port"] || Map.get(edit_set, :port, "1883")

    # File uploads are now handled in the component

    # Ensure host is not empty
    if host && host != "" do
      # Use the provided name from the form or generate one if empty
      name =
        if set_params["name"] && set_params["name"] != "" do
          set_params["name"]
        else
          "#{protocol}_#{host}_#{port}" |> String.replace(".", "_")
        end

      # Check if a connection set with the same name already exists (excluding the current one being edited)
      name_exists =
        Enum.any?(socket.assigns.connection_sets, fn set ->
          set.name == name && set.name != old_name
        end)

      if name_exists do
        # If a connection set with the same name already exists, return an error
        {:noreply,
         socket
         |> put_flash(:error, "A broker with the name '#{name}' already exists")
         |> assign(
           :edit_connection_set,
           Map.put(edit_set, :name_error, "This name is already in use")
         )}
      else
        # Get existing variables from the edit_connection_set
        variables = Map.get(edit_set, :variables, [])

        updated_set = %{
          name: name,
          protocol: protocol,
          host: host,
          port: port,
          variables: variables,
          connections: Map.get(edit_set, :connections, []),
          ssl_enabled: set_params["ssl_enabled"] == "on",
          alpn: set_params["alpn"] || "",
          certificate_type: set_params["certificate_type"] || "ca_signed",
          ca_file: set_params["ca_file"] || "",
          client_cert_file: set_params["client_cert_file"] || "",
          client_key_file: set_params["client_key_file"] || ""
        }

        # If the name changed, we need to handle the trace table
        if name != old_name do
          # Get existing trace messages from the old broker
          old_trace_messages = Mqttable.TraceManager.get_messages(old_name)

          # Remove the old broker's trace table
          Mqttable.TraceManager.remove_broker(old_name)

          # Store all messages under the new broker name
          Enum.each(old_trace_messages, fn message ->
            Mqttable.TraceManager.store_message(name, message)
          end)
        end

        # Update the connection set in the list
        connection_sets =
          Enum.map(socket.assigns.connection_sets, fn set ->
            if set.name == old_name, do: updated_set, else: set
          end)

        # Save the updated connection sets to persistent storage
        save_connection_set(connection_sets)

        # Use the updated set directly as the active connection set
        active_connection_set = updated_set

        # Load initial trace messages for the (possibly renamed) broker (first page only)
        trace_messages =
          case Mqttable.TraceManager.get_messages_paginated(name, 50, 0) do
            {:ok, messages, _has_more} -> messages
            _error -> []
          end

        {:noreply,
         socket
         |> assign(:connection_sets, connection_sets)
         |> assign(:active_connection_set, active_connection_set)
         |> assign(:trace_messages, trace_messages)
         |> assign(:show_modal, false)
         |> assign(:edit_connection_set, nil)}
      end
    else
      # If host is empty, return without doing anything
      {:noreply, socket}
    end
  end

  @doc """
  Handles the delete_connection_set event.
  """
  def handle_delete_connection_set(socket, %{"name" => name}) do
    connection_sets = Enum.reject(socket.assigns.connection_sets, fn set -> set.name == name end)

    # Remove the trace table for this broker
    Mqttable.TraceManager.remove_broker(name)

    # Clean all UI state data related to this broker
    ConnectionSets.clean_broker_ui_state(name)

    # Select a new active broker if the deleted one was active
    current_active_name =
      if socket.assigns.active_connection_set,
        do: socket.assigns.active_connection_set.name,
        else: nil

    new_active_set =
      if current_active_name == name do
        # Deleted broker was active, select a replacement
        ConnectionHelpers.select_active_broker(connection_sets)
      else
        # Keep current active broker
        socket.assigns.active_connection_set
      end

    # Update UI state with new active broker
    new_active_name = if new_active_set, do: new_active_set.name, else: nil
    ConnectionSets.update_active_connection_set(new_active_name)

    # Load trace messages for the new active broker
    trace_messages =
      if new_active_name do
        Mqttable.TraceManager.get_messages(new_active_name)
      else
        []
      end

    # Save the updated connection sets to persistent storage
    save_connection_set(connection_sets)

    # For critical operations like deletion, force an immediate save
    ConnectionSets.save_to_disk()

    {:noreply,
     socket
     |> assign(:connection_sets, connection_sets)
     |> assign(:active_connection_set, new_active_set)
     |> assign(:show_modal, false)
     |> assign(:edit_connection_set, nil)
     |> assign(:modal_type, nil)
     |> assign(:trace_messages, trace_messages)}
  end

  # The handle_select_connection_set function has been moved to StateManager.handle_broker_interaction
  # for code consolidation and is now called via StateManager.handle_toggle_set

  @doc """
  Saves connection sets to persistent storage.

  Updates the in-memory state and marks it as dirty.
  The GenServer will periodically save to disk based on its timer.
  """
  def save_connection_set(connection_sets) do
    # Update the in-memory state in the ConnectionSets server
    # This marks the state as dirty for the next periodic save
    ConnectionSets.update(connection_sets)

    # Synchronize the expanded sets state with the current connection sets
    ConnectionSets.sync_expanded_sets(connection_sets)

    # No need to force an immediate save to disk
    # The GenServer will save periodically
  end
end
