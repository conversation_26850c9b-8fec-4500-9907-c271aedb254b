defmodule MqttableWeb.TraceComponent do
  @moduledoc """
  LiveComponent for displaying MQTT trace data in a red-themed section.
  This component displays trace messages in a table format with smooth infinite scroll
  and provides functionality for viewing message details.
  """
  use MqttableWeb, :live_component
  import Bitwise
  require Logger

  # Number of messages to load per batch when scrolling
  @messages_per_batch 50

  @impl true
  def update(assigns, socket) do
    # Ensure we have a valid active_broker_name
    active_broker_name = assigns[:active_broker_name]

    # Check if the active broker has changed
    broker_changed =
      Map.has_key?(assigns, :active_broker_name) &&
        socket.assigns[:active_broker_name] != active_broker_name

    # Load filter state for the current broker
    filter_state = load_filter_state(active_broker_name)

    socket =
      socket
      |> assign(assigns)
      |> assign_new(:active_tab, fn -> "message_details" end)
      |> assign_new(:selected_message, fn -> nil end)
      |> assign_new(:loading_older, fn -> false end)
      |> assign_new(:has_older_messages, fn -> true end)
      |> assign_new(:auto_scroll_enabled, fn -> true end)
      |> assign_new(:oldest_loaded_timestamp, fn -> nil end)
      # New filter state assigns
      |> assign(:topic_filter, filter_state.topic_filter)
      |> assign(:payload_filter, filter_state.payload_filter)
      |> assign(:selected_client_ids, filter_state.selected_client_ids)
      |> assign(:ignore_ping_packets, filter_state.ignore_ping_packets)
      |> assign(:available_client_ids, get_broker_client_ids(active_broker_name))

    # Initialize or update the stream based on trace_messages
    socket =
      if Map.has_key?(assigns, :trace_messages) do
        # Check if stream exists and if this is a new message being added (real-time update)
        current_messages = get_current_stream_messages(socket)
        raw_messages = assigns.trace_messages || []

        # Apply filters to the messages
        new_messages = apply_filters(raw_messages, socket.assigns)

        cond do
          # Stream doesn't exist yet - initialize it
          !Map.has_key?(socket.assigns, :streams) ->
            socket
            |> initialize_stream(new_messages)

          # Broker changed - completely reinitialize the stream and reset state
          broker_changed ->
            # Reload filter state for the new broker
            new_filter_state = load_filter_state(active_broker_name)
            new_client_ids = get_broker_client_ids(active_broker_name)

            socket
            |> assign(:selected_message, nil)
            |> assign(:loading_older, false)
            |> assign(:has_older_messages, true)
            |> assign(:auto_scroll_enabled, true)
            |> assign(:oldest_loaded_timestamp, nil)
            # Update filter state for new broker
            |> assign(:topic_filter, new_filter_state.topic_filter)
            |> assign(:payload_filter, new_filter_state.payload_filter)
            |> assign(:selected_client_ids, new_filter_state.selected_client_ids)
            |> assign(:ignore_ping_packets, new_filter_state.ignore_ping_packets)
            |> assign(:available_client_ids, new_client_ids)
            |> initialize_stream(new_messages)

          # New messages detected - reinitialize stream to avoid duplicates
          length(new_messages) > length(current_messages) ->
            # Check if the currently selected message is still in the new messages
            selected_message = socket.assigns[:selected_message]

            preserved_selected_message =
              if selected_message do
                Enum.find(new_messages, fn msg -> msg.id == selected_message.id end)
              else
                nil
              end

            socket
            |> initialize_stream(new_messages)
            |> assign(:selected_message, preserved_selected_message)
            |> maybe_update_auto_scroll()

          # Messages changed but not more - reinitialize
          new_messages != current_messages ->
            # Check if the currently selected message is still in the new messages
            selected_message = socket.assigns[:selected_message]

            preserved_selected_message =
              if selected_message do
                Enum.find(new_messages, fn msg -> msg.id == selected_message.id end)
              else
                nil
              end

            socket
            |> initialize_stream(new_messages)
            |> assign(:selected_message, preserved_selected_message)

          # No changes
          true ->
            socket
        end
      else
        # No trace_messages in assigns - ensure stream is initialized empty
        if !Map.has_key?(socket.assigns, :streams) do
          initialize_stream(socket, [])
        else
          socket
        end
      end

    {:ok, socket}
  end

  @impl true
  def render(assigns) do
    ~H"""
    <div class="flex-grow flex flex-col overflow-hidden gap-2 main-content-area relative">
      <!-- trace section -->
      <div class="w-full flex flex-col bg-base-100 p-2">
        <!-- Filter controls -->
        <div class="mb-4">
          <!-- Row 1: Topic and Payload filters with Clear button -->
          <div class="flex gap-2 mb-2">
            <input
              type="text"
              placeholder="Filter by topic..."
              value={@topic_filter}
              class="input input-sm input-bordered flex-1"
              phx-keyup="topic_filter_changed"
              phx-target={@myself}
            />
            <input
              type="text"
              placeholder="Filter by payload..."
              value={@payload_filter}
              class="input input-sm input-bordered flex-1"
              phx-keyup="payload_filter_changed"
              phx-target={@myself}
            />
            <button
              class="btn btn-sm btn-outline btn-error"
              phx-click="clear_trace"
              phx-target={@myself}
            >
              <.icon name="hero-trash" class="size-4 mr-0" />
            </button>
          </div>
          
    <!-- Row 2: Client ID filter and PING ignore option -->
          <div class="flex gap-2 items-center">
            <!-- Client ID filter dropdown -->
            <div class="dropdown dropdown-bottom flex-1 border border-base-100 rounded-lg">
              <div tabindex="0" role="button" class="btn btn-sm btn-outline w-full">
                <img src="/images/device.svg" alt="Client" class="w-4 h-4 mr-2" />
                <span class="flex-1 text-left overflow-hidden text-ellipsis whitespace-nowrap">
                  {if Enum.empty?(@selected_client_ids) or
                        length(@selected_client_ids) == length(@available_client_ids),
                      do: "All Clients",
                      else: "#{length(@selected_client_ids)} Clients Selected"}
                </span>
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <path d="m6 9 6 6 6-6" />
                </svg>
              </div>
              <div
                tabindex="0"
                class="dropdown-content z-[1] menu p-2 shadow bg-base-100 rounded-box w-full max-h-60 overflow-auto"
              >
                <div class="form-control">
                  <label class="label cursor-pointer justify-start">
                    <input
                      type="checkbox"
                      class="checkbox checkbox-sm"
                      phx-click="toggle_all_clients"
                      phx-target={@myself}
                      checked={
                        Enum.empty?(@selected_client_ids) or
                          length(@selected_client_ids) == length(@available_client_ids)
                      }
                    />
                    <img src="/images/device.svg" alt="Client" class="w-3 h-3 mr-1" />
                    <span class="label-text ml-1">All Clients</span>
                  </label>
                  <%= for client_id <- @available_client_ids do %>
                    <label class="label cursor-pointer justify-start">
                      <input
                        type="checkbox"
                        class="checkbox checkbox-sm"
                        phx-click="toggle_client_id"
                        phx-value-client_id={client_id}
                        phx-target={@myself}
                        checked={client_id in @selected_client_ids}
                      />
                      <span class="label-text ml-2 font-mono text-xs">{client_id}</span>
                    </label>
                  <% end %>
                </div>
              </div>
            </div>
            
    <!-- PING packet ignore option -->
            <div class="form-control">
              <label class="label cursor-pointer">
                <input
                  type="checkbox"
                  class="checkbox checkbox-sm"
                  phx-click="toggle_ignore_ping"
                  phx-target={@myself}
                  checked={@ignore_ping_packets}
                />
                <span class="label-text ml-2">Ignore PING</span>
              </label>
            </div>
          </div>
        </div>
        
    <!-- Trace message table -->
        <div class="trace-message-table-container">
          <div class="trace-message-table-wrapper">
            <div id="trace-table-highlight-container" phx-hook="TraceRowHighlight">
              <table class="table table-xs table-pin-rows table-pin-cols table-zebra w-full compact">
                <thead>
                  <tr>
                    <th class="w-16">Packet ID</th>
                    <th class="w-36">Timestamp</th>
                    <th class="w-32">ClientID</th>
                    <th class="w-20">Direction</th>
                    <th class="w-24">Type</th>
                    <th class="w-32">Topic</th>
                    <th class="w-1/3">Payload (Preview)</th>
                    <th class="w-16">Length</th>
                    <th class="w-12">QoS</th>
                    <th class="w-16">Retain</th>
                  </tr>
                </thead>
                <tbody
                  id="trace-message-table-body"
                  phx-update="stream"
                  phx-viewport-top="scroll-to-top"
                  phx-viewport-bottom="load-older-messages"
                  phx-target={@myself}
                  phx-hook="TraceTableGSAP"
                >
                  <%= for {id, message} <- @streams.trace_messages do %>
                    <tr
                      id={id}
                      class={"hover cursor-pointer trace-message-row #{direction_indicator_class(message.direction)} #{if @selected_message && to_string(@selected_message.id) == to_string(message.id), do: "trace-message-selected", else: ""} #{if Map.get(message, :is_new, false), do: "trace-message-new", else: ""}"}
                      phx-click="select_message"
                      phx-value-id={message.id}
                      phx-target={@myself}
                    >
                      <td>
                        <span class="trace-packet-id">{message.packet_id || "N/A"}</span>
                      </td>
                      <td>
                        <span class="trace-timestamp">{format_timestamp(message.timestamp)}</span>
                      </td>
                      <td>
                        <span class="badge badge-ghost badge-sm trace-client-id">
                          {message.client_id || "unknown"}
                        </span>
                      </td>
                      <td>
                        <span class={"badge badge-sm #{direction_badge_class(message.direction)} trace-direction"}>
                          <.icon name={direction_icon(message.direction)} class="size-3 mr-0" />
                          {message.direction || "N/A"}
                        </span>
                      </td>
                      <td>
                        <span class={"badge badge-sm badge-outline #{message_type_badge_class(message.type)} trace-message-type"}>
                          {message.type}
                        </span>
                      </td>
                      <td class="table-cell-truncate" title={message.topic}>
                        <span class="trace-topic">{message.topic || "N/A"}</span>
                      </td>
                      <td class="payload-preview-truncate font-mono" title={message.payload}>
                        <span class="trace-payload">
                          {String.slice(message.payload || "", 0, 100) <>
                            if String.length(message.payload || "") > 100, do: "...", else: ""}
                        </span>
                      </td>
                      <td>
                        <span class="trace-payload-length">{message.payload_length}</span>
                      </td>
                      <td>
                        <span class="trace-qos">{message.qos}</span>
                      </td>
                      <td>
                        <span class="trace-retain">{if message.retain, do: "Yes", else: "No"}</span>
                      </td>
                    </tr>
                  <% end %>
                  
    <!-- Loading indicator for older messages at the bottom -->
                  <%= if @loading_older do %>
                    <tr id="loading-older-row" class="trace-loading-row">
                      <td colspan="10" class="text-center py-4">
                        <span class="loading loading-spinner loading-sm"></span>
                        <span class="ml-2 text-sm text-gray-500">Loading older messages...</span>
                      </td>
                    </tr>
                  <% end %>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
    """
  end

  @impl true
  def handle_event("load-older-messages", _params, socket) do
    # Only load if we're not already loading and there are older messages
    if socket.assigns.loading_older || !socket.assigns.has_older_messages do
      {:noreply, socket}
    else
      # Set loading state
      socket = assign(socket, :loading_older, true)

      # Get the active broker name
      broker_name = socket.assigns[:active_broker_name]

      if broker_name do
        # Get the oldest timestamp from current stream to use as offset
        oldest_timestamp = socket.assigns.oldest_loaded_timestamp

        # Load older messages
        case load_older_messages(broker_name, oldest_timestamp, socket.assigns) do
          {:ok, older_messages, has_more_older} ->
            # Add older messages to the bottom of the stream (they are older)
            socket =
              socket
              |> add_older_messages_to_stream(older_messages)
              |> assign(:has_older_messages, has_more_older)
              |> assign(:loading_older, false)
              |> update_oldest_timestamp(older_messages)

            {:noreply, socket}

          _error ->
            # Handle error case
            socket =
              socket
              |> assign(:loading_older, false)
              |> put_flash(:error, "Failed to load older messages")

            {:noreply, socket}
        end
      else
        # No broker name available
        socket = assign(socket, :loading_older, false)
        {:noreply, socket}
      end
    end
  end

  @impl true
  def handle_event("scroll-to-top", _params, socket) do
    # User scrolled to top - enable auto-scroll for new messages (keep at top)
    socket = assign(socket, :auto_scroll_enabled, true)
    {:noreply, socket}
  end

  @impl true
  def handle_event("select_message", %{"id" => id}, socket) do
    # Find the selected message by ID
    id = String.to_integer(id)

    # Try to find the message from the original trace_messages first (more reliable)
    # since the stream state can be unstable due to rapid updates
    selected_message =
      case socket.assigns[:trace_messages] do
        messages when is_list(messages) ->
          # Apply the same filters that were used to create the stream
          filtered_messages = apply_filters(messages, socket.assigns)
          Enum.find(filtered_messages, fn message -> message.id == id end)

        _ ->
          # Fallback: try to find from the stream
          case socket.assigns.streams.trace_messages do
            %Phoenix.LiveView.LiveStream{inserts: inserts} ->
              inserts
              |> Enum.find_value(fn {_stream_id, message} ->
                if message.id == id, do: message, else: nil
              end)

            _ ->
              nil
          end
      end

    # Send message to parent LiveView to open detail modal
    send(self(), {:open_detail_modal, selected_message})

    # Keep for backward compatibility
    socket = assign(socket, :selected_message, selected_message)

    {:noreply, socket}
  end

  @impl true
  def handle_event("clear_trace", _params, socket) do
    # Get the active broker name from the parent assigns
    broker_name = socket.assigns[:active_broker_name]
    # Clear the trace messages from ETS table if broker name is available
    if broker_name do
      Mqttable.TraceManager.clear_messages(broker_name)
    end

    # Reset filter state to defaults
    default_state = default_filter_state()

    # Clear the stream and reset state
    socket =
      socket
      |> stream(:trace_messages, [], reset: true)
      |> assign(:selected_message, nil)
      |> assign(:loading_older, false)
      |> assign(:has_older_messages, true)
      |> assign(:auto_scroll_enabled, true)
      |> assign(:oldest_loaded_timestamp, nil)
      # Reset filter state
      |> assign(:topic_filter, default_state.topic_filter)
      |> assign(:payload_filter, default_state.payload_filter)
      |> assign(:selected_client_ids, default_state.selected_client_ids)
      |> assign(:ignore_ping_packets, default_state.ignore_ping_packets)

    # Save the reset filter state
    save_filter_state(socket)

    # Send message to parent LiveView to clear its trace_messages state
    send(self(), {:clear_trace_messages, broker_name})

    {:noreply, socket}
  end

  @impl true
  def handle_event("topic_filter_changed", %{"value" => value}, socket) do
    socket =
      socket
      |> assign(:topic_filter, value)
      |> refresh_filtered_messages()

    save_filter_state(socket)
    {:noreply, socket}
  end

  @impl true
  def handle_event("payload_filter_changed", %{"value" => value}, socket) do
    socket =
      socket
      |> assign(:payload_filter, value)
      |> refresh_filtered_messages()

    save_filter_state(socket)
    {:noreply, socket}
  end

  @impl true
  def handle_event("toggle_all_clients", _params, socket) do
    # If all clients are currently selected or none selected, toggle to select none
    # Otherwise, select all clients
    selected_client_ids =
      if Enum.empty?(socket.assigns.selected_client_ids) or
           length(socket.assigns.selected_client_ids) ==
             length(socket.assigns.available_client_ids) do
        []
      else
        socket.assigns.available_client_ids
      end

    socket =
      socket
      |> assign(:selected_client_ids, selected_client_ids)
      |> refresh_filtered_messages()

    save_filter_state(socket)
    {:noreply, socket}
  end

  @impl true
  def handle_event("toggle_client_id", %{"client_id" => client_id}, socket) do
    selected_client_ids = socket.assigns.selected_client_ids

    # Toggle the client ID in the selected client IDs list
    updated_client_ids =
      if client_id in selected_client_ids do
        List.delete(selected_client_ids, client_id)
      else
        [client_id | selected_client_ids]
      end

    socket =
      socket
      |> assign(:selected_client_ids, updated_client_ids)
      |> refresh_filtered_messages()

    save_filter_state(socket)
    {:noreply, socket}
  end

  @impl true
  def handle_event("toggle_ignore_ping", _params, socket) do
    ignore_ping_packets = !socket.assigns.ignore_ping_packets

    socket =
      socket
      |> assign(:ignore_ping_packets, ignore_ping_packets)
      |> refresh_filtered_messages()

    save_filter_state(socket)
    {:noreply, socket}
  end

  @impl true
  def handle_event("switch_payload_view", %{"type" => type}, socket) do
    # Switch payload view type between plaintext, json, and hex
    valid_types = ["plaintext", "json", "hex"]

    payload_view_type = if type in valid_types, do: type, else: "plaintext"

    {:noreply, assign(socket, :payload_view_type, payload_view_type)}
  end

  @impl true
  def handle_event("view_payload_as_text", _params, socket) do
    # Legacy event handler - redirect to new switch handler
    {:noreply, assign(socket, :payload_view_type, "plaintext")}
  end

  @impl true
  def handle_event("view_payload_as_json", _params, socket) do
    # Legacy event handler - redirect to new switch handler
    {:noreply, assign(socket, :payload_view_type, "json")}
  end

  @impl true
  def handle_event("view_payload_as_hex", _params, socket) do
    # Legacy event handler - redirect to new switch handler
    {:noreply, assign(socket, :payload_view_type, "hex")}
  end

  @impl true
  def handle_event("switch_tab", %{"tab" => tab}, socket) do
    # Switch to the selected tab and auto-switch to message details when a message is selected
    active_tab =
      if tab == "message_details" && socket.assigns.selected_message,
        do: "message_details",
        else: tab

    {:noreply, assign(socket, :active_tab, active_tab)}
  end

  # Stream management helper functions

  # Get current messages from the stream
  defp get_current_stream_messages(socket) do
    case socket.assigns do
      %{streams: %{trace_messages: %Phoenix.LiveView.LiveStream{inserts: inserts}}} ->
        Enum.map(inserts, fn {_id, message} -> message end)

      _ ->
        []
    end
  end

  # Initialize the stream with initial messages
  defp initialize_stream(socket, messages) do
    # Take the most recent messages for initial load
    initial_messages = Enum.take(messages, @messages_per_batch)

    socket
    |> stream(:trace_messages, initial_messages, reset: true)
    |> assign(:oldest_loaded_timestamp, get_oldest_timestamp(initial_messages))
    |> assign(:auto_scroll_enabled, true)
  end

  # Add older messages to the stream (at the bottom)
  defp add_older_messages_to_stream(socket, older_messages) do
    # Add each older message to the stream at the bottom
    Enum.reduce(older_messages, socket, fn message, acc_socket ->
      stream_insert(acc_socket, :trace_messages, message, at: -1)
    end)
  end

  # Update auto-scroll state based on new messages
  defp maybe_update_auto_scroll(socket) do
    # If auto-scroll is enabled, we'll let the client-side handle scrolling
    # This function can be extended to add more logic if needed
    socket
  end

  # Update the oldest loaded timestamp
  defp update_oldest_timestamp(socket, messages) do
    oldest = get_oldest_timestamp(messages)
    current_oldest = socket.assigns.oldest_loaded_timestamp

    new_oldest =
      case {oldest, current_oldest} do
        {nil, current} -> current
        {new_ts, nil} -> new_ts
        {new_ts, current_ts} -> if new_ts < current_ts, do: new_ts, else: current_ts
      end

    assign(socket, :oldest_loaded_timestamp, new_oldest)
  end

  # Get the oldest timestamp from a list of messages
  defp get_oldest_timestamp([]), do: nil

  defp get_oldest_timestamp(messages) do
    messages
    |> Enum.map(& &1.timestamp)
    |> Enum.filter(&(&1 != nil))
    |> Enum.min(fn -> nil end)
  end

  # Load older messages from the TraceManager
  defp load_older_messages(broker_name, oldest_timestamp, socket_assigns) do
    # Calculate offset based on oldest timestamp
    # For now, we'll use a simple approach and get all messages, then filter
    case Mqttable.TraceManager.get_messages(broker_name) do
      messages when is_list(messages) ->
        # Apply filters first
        filtered_messages = apply_filters(messages, socket_assigns)

        # Filter messages older than the oldest loaded timestamp
        older_messages =
          if oldest_timestamp do
            filtered_messages
            |> Enum.filter(fn msg ->
              msg.timestamp && msg.timestamp < oldest_timestamp
            end)
            |> Enum.take(@messages_per_batch)
          else
            # If no oldest timestamp, take the oldest messages
            filtered_messages
            |> Enum.reverse()
            |> Enum.take(@messages_per_batch)
            |> Enum.reverse()
          end

        has_more = length(older_messages) == @messages_per_batch
        {:ok, older_messages, has_more}

      _error ->
        {:error, "Failed to load messages"}
    end
  end

  # Helper function to get client IDs for a specific broker
  defp get_broker_client_ids(nil), do: []

  defp get_broker_client_ids(broker_name) do
    # Get all connection sets
    connection_sets = Mqttable.ConnectionSets.get_all()

    # Find the broker by name
    broker =
      Enum.find(connection_sets, fn set ->
        Map.get(set, :name) == broker_name
      end)

    case broker do
      nil ->
        []

      broker ->
        # Extract client IDs from connections in this broker
        broker
        |> Map.get(:connections, [])
        |> Enum.map(fn conn -> Map.get(conn, :client_id) end)
        |> Enum.filter(&(&1 != nil && &1 != ""))
        |> Enum.sort()
    end
  end

  # Helper function to load filter state for a broker
  defp load_filter_state(nil) do
    default_filter_state()
  end

  defp load_filter_state(broker_name) do
    # Get UI state from ConnectionSets
    ui_state = Mqttable.ConnectionSets.get_ui_state()
    trace_filters = Map.get(ui_state, :trace_filters, %{})
    broker_filters = Map.get(trace_filters, broker_name, %{})

    # Merge with defaults
    default_filter_state()
    |> Map.merge(broker_filters)
  end

  # Helper function to save filter state for current broker
  defp save_filter_state(socket) do
    broker_name = socket.assigns[:active_broker_name]

    if broker_name do
      # Get current UI state
      ui_state = Mqttable.ConnectionSets.get_ui_state()
      trace_filters = Map.get(ui_state, :trace_filters, %{})

      # Update filter state for this broker
      broker_filter_state = %{
        topic_filter: socket.assigns.topic_filter,
        payload_filter: socket.assigns.payload_filter,
        selected_client_ids: socket.assigns.selected_client_ids,
        ignore_ping_packets: socket.assigns.ignore_ping_packets
      }

      updated_trace_filters = Map.put(trace_filters, broker_name, broker_filter_state)
      updated_ui_state = Map.put(ui_state, :trace_filters, updated_trace_filters)

      # Save to ConnectionSets
      Mqttable.ConnectionSets.update_ui_state(updated_ui_state)
    end
  end

  # Helper function to get default filter state
  defp default_filter_state do
    %{
      topic_filter: "",
      payload_filter: "",
      selected_client_ids: [],
      ignore_ping_packets: true
    }
  end

  # Helper function to apply filters to trace messages
  defp apply_filters(messages, assigns) when is_list(messages) do
    messages
    |> filter_by_ping_packets(assigns[:ignore_ping_packets])
    |> filter_by_topic(assigns[:topic_filter])
    |> filter_by_payload(assigns[:payload_filter])
    |> filter_by_client_ids(assigns[:selected_client_ids], assigns[:available_client_ids])
  end

  defp apply_filters(messages, _assigns), do: messages

  # Filter out PING packets if ignore_ping_packets is true
  defp filter_by_ping_packets(messages, true) do
    Enum.filter(messages, fn message ->
      message.type not in ["PINGREQ", "PINGRESP"]
    end)
  end

  defp filter_by_ping_packets(messages, _), do: messages

  # Filter by topic if topic_filter is not empty
  defp filter_by_topic(messages, topic_filter)
       when is_binary(topic_filter) and topic_filter != "" do
    Enum.filter(messages, fn message ->
      topic = message.topic || ""
      # Use MQTT topic matching logic instead of simple string contains
      # This supports MQTT wildcards like + and #
      # Parameters: match(topic_name, topic_filter)
      Mqttable.MqttClient.Topic.match(topic, topic_filter)
    end)
  end

  defp filter_by_topic(messages, _), do: messages

  # Filter by payload if payload_filter is not empty
  defp filter_by_payload(messages, payload_filter)
       when is_binary(payload_filter) and payload_filter != "" do
    Enum.filter(messages, fn message ->
      payload = message.payload || ""
      String.contains?(String.downcase(payload), String.downcase(payload_filter))
    end)
  end

  defp filter_by_payload(messages, _), do: messages

  # Filter by client IDs if specific client IDs are selected
  defp filter_by_client_ids(messages, selected_client_ids, available_client_ids)
       when is_list(selected_client_ids) and is_list(available_client_ids) do
    # If no client IDs are selected or all available client IDs are selected, show all
    if Enum.empty?(selected_client_ids) or
         length(selected_client_ids) == length(available_client_ids) do
      messages
    else
      Enum.filter(messages, fn message ->
        message.client_id in selected_client_ids
      end)
    end
  end

  defp filter_by_client_ids(messages, _, _), do: messages

  # Helper function to refresh filtered messages when filter state changes
  defp refresh_filtered_messages(socket) do
    broker_name = socket.assigns[:active_broker_name]

    if broker_name do
      # Get all messages from TraceManager
      case Mqttable.TraceManager.get_messages(broker_name) do
        messages when is_list(messages) ->
          # Apply current filters
          filtered_messages = apply_filters(messages, socket.assigns)

          # Check if the currently selected message is still in the filtered results
          selected_message = socket.assigns[:selected_message]

          preserved_selected_message =
            if selected_message do
              Enum.find(filtered_messages, fn msg -> msg.id == selected_message.id end)
            else
              nil
            end

          # Reinitialize the stream with filtered messages
          socket
          |> initialize_stream(filtered_messages)
          |> assign(:selected_message, preserved_selected_message)

        _ ->
          socket
      end
    else
      socket
    end
  end

  # Helper function to determine badge class based on direction
  defp direction_badge_class(direction) do
    case direction do
      "IN" -> "badge-success"
      "OUT" -> "badge-warning"
      _ -> "badge-ghost"
    end
  end

  # Helper function to determine badge class based on message type
  defp message_type_badge_class(type) do
    case type do
      # Publish-related packets
      "PUBLISH" -> "badge-info"
      "PUBACK" -> "badge-info"
      "PUBREC" -> "badge-info"
      "PUBREL" -> "badge-info"
      "PUBCOMP" -> "badge-info"
      # Subscribe-related packets
      "SUBSCRIBE" -> "badge-accent"
      "SUBACK" -> "badge-accent"
      "UNSUBSCRIBE" -> "badge-accent"
      "UNSUBACK" -> "badge-accent"
      # Connection-related packets
      "CONNECT" -> "badge-success"
      "CONNACK" -> "badge-success"
      "DISCONNECT" -> "badge-warning"
      # Ping packets
      "PINGREQ" -> "badge-secondary"
      "PINGRESP" -> "badge-secondary"
      # Auth packet
      "AUTH" -> "badge-primary"
      # Unknown packets
      _ -> "badge-ghost"
    end
  end

  # Helper function to determine badge class based on QoS level
  defp qos_badge_class(qos) do
    case qos do
      0 -> "badge-ghost"
      1 -> "badge-info"
      2 -> "badge-warning"
      _ -> "badge-error"
    end
  end

  # Helper function to format MQTT properties for display
  defp format_mqtt_properties(properties) when is_map(properties) do
    properties
    |> Enum.map(fn {key, value} -> "#{key}: #{inspect(value)}" end)
    |> Enum.join("\n")
  end

  defp format_mqtt_properties(_), do: ""

  # Helper function to format timestamp for display (fixed 18 characters)
  defp format_timestamp(timestamp) when is_binary(timestamp) do
    case DateTime.from_iso8601(timestamp) do
      {:ok, dt, _offset} ->
        # Format as MM-DD HH:MM:SS.mmm (exactly 18 characters)
        month = dt.month |> Integer.to_string() |> String.pad_leading(2, "0")
        day = dt.day |> Integer.to_string() |> String.pad_leading(2, "0")
        hour = dt.hour |> Integer.to_string() |> String.pad_leading(2, "0")
        minute = dt.minute |> Integer.to_string() |> String.pad_leading(2, "0")
        second = dt.second |> Integer.to_string() |> String.pad_leading(2, "0")

        millisecond =
          dt.microsecond
          |> elem(0)
          |> div(1000)
          |> Integer.to_string()
          |> String.pad_leading(3, "0")

        "#{month}-#{day} #{hour}:#{minute}:#{second}.#{millisecond}"

      _error ->
        # Ensure fallback is also 18 characters
        timestamp
        |> String.slice(0, 18)
        |> String.pad_trailing(18, " ")
    end
  end

  defp format_timestamp(timestamp) do
    # Ensure N/A is also 18 characters
    (timestamp || "N/A")
    |> String.slice(0, 18)
    |> String.pad_trailing(18, " ")
  end

  # Helper function to get direction icon
  defp direction_icon(direction) do
    case direction do
      "IN" -> "hero-chevron-double-left"
      "OUT" -> "hero-chevron-double-right"
      _ -> "hero-minus-circle"
    end
  end

  # Helper function to get direction indicator class (static, no animation)
  defp direction_indicator_class(direction) do
    case direction do
      "IN" -> "trace-message-in"
      "OUT" -> "trace-message-out"
      _ -> "trace-message-neutral"
    end
  end

  # Render packet details based on packet type
  defp render_packet_details(message, myself, payload_view_type) do
    assigns = %{message: message, myself: myself, payload_view_type: payload_view_type}

    ~H"""
    <div class="space-y-3">
      <!-- Header Card with Essential Info -->
      <div class="card card-compact bg-base-200 shadow-sm">
        <div class="card-body p-3">
          <!-- Primary Info Row -->
          <div class="flex items-center justify-between mb-2">
            <div class="flex items-center gap-3">
              <div class="flex items-center gap-2">
                <.icon name={direction_icon(@message.direction)} class="size-4 text-base-content/70" />
                <span class={"badge badge-sm #{direction_badge_class(@message.direction)}"}>
                  {@message.direction || "N/A"}
                </span>
              </div>
              <div class="divider divider-horizontal mx-0"></div>
              <span class={"badge badge-sm #{message_type_badge_class(@message.type)}"}>
                {@message.type}
              </span>
            </div>
            <div class="text-xs text-base-content/60 font-mono">
              {format_timestamp(@message.timestamp)}
            </div>
          </div>
          
    <!-- Secondary Info Row -->
          <div class="grid grid-cols-2 gap-4 text-xs">
            <div class="flex items-center gap-2">
              <.icon name="hero-user" class="size-3 text-base-content/50" />
              <span class="text-base-content/70">Client:</span>
              <span class="font-mono text-base-content truncate">
                {@message.client_id || "unknown"}
              </span>
            </div>
            <%= if @message.packet_id do %>
              <div class="flex items-center gap-2">
                <.icon name="hero-hashtag" class="size-3 text-base-content/50" />
                <span class="text-base-content/70">Packet ID:</span>
                <span class="font-mono text-base-content">{@message.packet_id}</span>
              </div>
            <% end %>
          </div>
        </div>
      </div>
      
    <!-- Packet-specific Details Card -->
      <div class="card card-compact bg-base-100 border border-base-300">
        <div class="card-body p-3">
          <%= case @message.type do %>
            <% "PUBLISH" -> %>
              {render_publish_details(@message, @myself, @payload_view_type)}
            <% "SUBSCRIBE" -> %>
              {render_subscribe_details(@message)}
            <% "UNSUBSCRIBE" -> %>
              {render_unsubscribe_details(@message)}
            <% "SUBACK" -> %>
              {render_suback_details(@message)}
            <% "UNSUBACK" -> %>
              {render_unsuback_details(@message)}
            <% type when type in ["PUBACK", "PUBREC", "PUBREL", "PUBCOMP"] -> %>
              {render_pub_ack_details(@message)}
            <% "CONNACK" -> %>
              {render_connack_details(@message)}
            <% "CONNECT" -> %>
              {render_connect_details(@message, @myself, @payload_view_type)}
            <% "DISCONNECT" -> %>
              {render_disconnect_details(@message)}
            <% "AUTH" -> %>
              {render_auth_details(@message)}
            <% _ -> %>
              {render_generic_details(@message, @myself, @payload_view_type)}
          <% end %>
        </div>
      </div>
      
    <!-- MQTT Properties Card (if present) -->
      <%= if is_map(@message.properties) && map_size(@message.properties) > 0 do %>
        <div class="card card-compact bg-base-100 border border-base-300">
          <div class="card-body p-3">
            <div class="flex items-center gap-2 mb-2">
              <.icon name="hero-cog-6-tooth" class="size-4 text-base-content/70" />
              <h4 class="text-sm font-medium text-base-content">MQTT Properties</h4>
            </div>
            <div class="bg-base-200 p-2 rounded-lg">
              <pre class="text-xs font-mono text-base-content leading-relaxed whitespace-pre-wrap">{format_mqtt_properties(@message.properties)}</pre>
            </div>
          </div>
        </div>
      <% end %>
    </div>
    """
  end

  # Render PUBLISH packet details
  defp render_publish_details(message, myself, payload_view_type) do
    assigns = %{message: message, myself: myself, payload_view_type: payload_view_type}

    ~H"""
    <div class="space-y-3">
      <!-- Topic Section -->
      <div class="space-y-2">
        <div class="flex items-center gap-2">
          <.icon name="hero-chat-bubble-left-right" class="size-4 text-base-content/70" />
          <h4 class="text-sm font-medium text-base-content">Topic</h4>
        </div>
        <div class="bg-base-200 p-2 rounded-lg">
          <code class="text-sm font-mono text-base-content break-all">
            {@message.topic || "N/A"}
          </code>
        </div>
      </div>
      
    <!-- Message Properties -->
      <div class="grid grid-cols-3 gap-3">
        <div class="stat stat-compact bg-base-200 rounded-lg">
          <div class="stat-title text-xs">QoS Level</div>
          <div class="stat-value text-lg">
            <span class={"badge badge-lg #{qos_badge_class(@message.qos)}"}>
              {@message.qos}
            </span>
          </div>
        </div>

        <div class="stat stat-compact bg-base-200 rounded-lg">
          <div class="stat-title text-xs">Retain</div>
          <div class="stat-value text-lg">
            <span class={"badge badge-lg #{if @message.retain, do: "badge-warning", else: "badge-ghost"}"}>
              {if @message.retain, do: "Yes", else: "No"}
            </span>
          </div>
        </div>

        <div class="stat stat-compact bg-base-200 rounded-lg">
          <div class="stat-title text-xs">Size</div>
          <div class="stat-value text-sm font-mono">
            {@message.payload_length} B
          </div>
        </div>
      </div>
      
    <!-- Payload Section -->
      <div class="space-y-2">
        <div class="flex items-center justify-between bg-base-200 p-2 rounded-t-lg">
          <div class="flex items-center gap-2">
            <.icon name="hero-document-text" class="size-4 text-base-content" />
            <h4 class="text-sm font-medium text-base-content">Payload</h4>
            <span class="text-xs text-base-content">
              (Length: {@message.payload_length || 0})
            </span>
          </div>
          <div class="tabs tabs-boxed tabs-xs bg-base-300">
            <button
              class={"tab tab-xs text-base-content #{if @payload_view_type == "plaintext", do: "tab-active", else: ""}"}
              phx-click="switch_payload_view"
              phx-value-type="plaintext"
              phx-target={@myself}
            >
              Text
            </button>
            <button
              class={"tab tab-xs text-base-content #{if @payload_view_type == "json", do: "tab-active", else: ""}"}
              phx-click="switch_payload_view"
              phx-value-type="json"
              phx-target={@myself}
            >
              JSON
            </button>
            <button
              class={"tab tab-xs text-base-content #{if @payload_view_type == "hex", do: "tab-active", else: ""}"}
              phx-click="switch_payload_view"
              phx-value-type="hex"
              phx-target={@myself}
            >
              Hex
            </button>
          </div>
        </div>

        <%= if @message.payload_length && @message.payload_length > 0 do %>
          <%= if @payload_view_type == "json" do %>
            <div class="bg-base-100 rounded-b-lg">
              <.render_json_viewer
                payload={@message.payload || ""}
                id={"publish-payload-#{@message.id}"}
              />
            </div>
          <% else %>
            <pre class="text-xs leading-relaxed px-4 py-2 max-h-32 overflow-y-auto whitespace-pre-wrap bg-base-100 rounded-b-lg"><%= format_payload_for_display(@message.payload || "", @payload_view_type || "plaintext") %></pre>
          <% end %>
        <% else %>
          <div class="alert alert-info alert-sm">
            <.icon name="hero-information-circle" class="size-4" />
            <span class="text-xs">
              <%= if @message.payload_length == 0 do %>
                Empty payload (0 bytes)
              <% else %>
                No payload data available
              <% end %>
            </span>
          </div>
        <% end %>
      </div>
    </div>
    """
  end

  # Render SUBSCRIBE packet details
  defp render_subscribe_details(message) do
    assigns = %{message: message}

    ~H"""
    <div class="space-y-3">
      <!-- Topic Filters Section -->
      <div class="space-y-2">
        <div class="flex items-center gap-2">
          <.icon name="hero-plus-circle" class="size-4 text-base-content/70" />
          <h4 class="text-sm font-medium text-base-content">Topic Filters</h4>
        </div>
        <div class="bg-base-200 p-2 rounded-lg">
          <code class="text-sm font-mono text-base-content break-all">
            {@message.topic || "N/A"}
          </code>
        </div>
      </div>
      
    <!-- Subscription Details -->
      <%= if @message.extra_info && is_list(@message.extra_info) do %>
        <div class="space-y-2">
          <div class="flex items-center gap-2">
            <.icon name="hero-list-bullet" class="size-4 text-base-content/70" />
            <h4 class="text-sm font-medium text-base-content">Subscription Details</h4>
          </div>
          <div class="space-y-1">
            <%= for {topic, opts} <- @message.extra_info do %>
              <div class="flex items-center justify-between p-2 bg-base-200 rounded">
                <code class="text-xs font-mono text-base-content flex-1 mr-2">{topic}</code>
                <div class="flex items-center gap-2">
                  <%= if is_map(opts) do %>
                    <span class={"badge badge-sm #{qos_badge_class(Map.get(opts, :qos, 0))}"}>
                      QoS {Map.get(opts, :qos, 0)}
                    </span>
                    <%= if Map.get(opts, :nl, 0) == 1 do %>
                      <span class="badge badge-xs badge-info">NL</span>
                    <% end %>
                    <%= if Map.get(opts, :rap, 0) == 1 do %>
                      <span class="badge badge-xs badge-warning">RAP</span>
                    <% end %>
                    <%= if Map.get(opts, :rh, 0) > 0 do %>
                      <span class="badge badge-xs badge-secondary">RH:{Map.get(opts, :rh)}</span>
                    <% end %>
                  <% else %>
                    <span class={"badge badge-sm #{qos_badge_class(opts)}"}>
                      QoS {opts}
                    </span>
                  <% end %>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      <% end %>
    </div>
    """
  end

  # Render UNSUBSCRIBE packet details
  defp render_unsubscribe_details(message) do
    assigns = %{message: message}

    ~H"""
    <div class="space-y-2">
      <div class="flex items-center gap-2">
        <.icon name="hero-minus-circle" class="size-4 text-base-content/70" />
        <h4 class="text-sm font-medium text-base-content">Topic Filters</h4>
      </div>
      <div class="bg-base-200 p-2 rounded-lg">
        <code class="text-sm font-mono text-base-content break-all">
          {@message.topic || "N/A"}
        </code>
      </div>
    </div>
    """
  end

  # Render SUBACK packet details with error highlighting
  defp render_suback_details(message) do
    assigns = %{message: message}

    ~H"""
    <div class="space-y-3">
      <!-- Status Overview -->
      <div class="space-y-2">
        <div class="flex items-center gap-2">
          <.icon name="hero-check-circle" class="size-4 text-base-content/70" />
          <h4 class="text-sm font-medium text-base-content">Subscription Status</h4>
        </div>
        <div class="alert alert-sm">
          <span class={get_ack_status_class(@message.reason_code)}>
            {format_suback_status(@message.reason_code)}
          </span>
        </div>
      </div>
      
    <!-- Reason Codes Details -->
      <%= if @message.reason_code && is_list(@message.reason_code) do %>
        <div class="space-y-2">
          <div class="flex items-center gap-2">
            <.icon name="hero-list-bullet" class="size-4 text-base-content/70" />
            <h4 class="text-sm font-medium text-base-content">Reason Codes</h4>
          </div>
          <div class="space-y-1">
            <%= for {code, index} <- Enum.with_index(@message.reason_code) do %>
              <div class={"card card-compact #{get_reason_code_class(code)}"}>
                <div class="card-body p-2">
                  <div class="flex justify-between items-center">
                    <span class="text-xs font-medium">Filter {index + 1}</span>
                    <code class="text-xs font-mono">{format_reason_code(code)}</code>
                  </div>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      <% end %>
    </div>
    """
  end

  # Render UNSUBACK packet details with error highlighting
  defp render_unsuback_details(message) do
    assigns = %{message: message}

    ~H"""
    <div class="space-y-3">
      <!-- Status Overview -->
      <div class="space-y-2">
        <div class="flex items-center gap-2">
          <.icon name="hero-x-circle" class="size-4 text-base-content/70" />
          <h4 class="text-sm font-medium text-base-content">Unsubscription Status</h4>
        </div>
        <div class="alert alert-sm">
          <span class={get_ack_status_class(@message.reason_code)}>
            {format_unsuback_status(@message.reason_code)}
          </span>
        </div>
      </div>
      
    <!-- Reason Codes Details -->
      <%= if @message.reason_code && is_list(@message.reason_code) do %>
        <div class="space-y-2">
          <div class="flex items-center gap-2">
            <.icon name="hero-list-bullet" class="size-4 text-base-content/70" />
            <h4 class="text-sm font-medium text-base-content">Reason Codes</h4>
          </div>
          <div class="space-y-1">
            <%= for {code, index} <- Enum.with_index(@message.reason_code) do %>
              <div class={"card card-compact #{get_reason_code_class(code)}"}>
                <div class="card-body p-2">
                  <div class="flex justify-between items-center">
                    <span class="text-xs font-medium">Filter {index + 1}</span>
                    <code class="text-xs font-mono">{format_reason_code(code)}</code>
                  </div>
                </div>
              </div>
            <% end %>
          </div>
        </div>
      <% end %>
    </div>
    """
  end

  # Render PUBACK/PUBREC/PUBREL/PUBCOMP packet details with error highlighting
  defp render_pub_ack_details(message) do
    assigns = %{message: message}

    ~H"""
    <div class="space-y-3">
      <!-- Status Overview -->
      <div class="space-y-2">
        <div class="flex items-center gap-2">
          <.icon name="hero-check-badge" class="size-4 text-base-content/70" />
          <h4 class="text-sm font-medium text-base-content">Acknowledgment Status</h4>
        </div>
        <div class="alert alert-sm">
          <span class={get_ack_status_class(@message.reason_code)}>
            {format_pub_ack_status(@message.reason_code)}
          </span>
        </div>
      </div>
      
    <!-- Reason Code Details -->
      <%= if @message.reason_code do %>
        <div class="space-y-2">
          <div class="flex items-center gap-2">
            <.icon name="hero-information-circle" class="size-4 text-base-content/70" />
            <h4 class="text-sm font-medium text-base-content">Reason Code</h4>
          </div>
          <div class={"card card-compact #{get_reason_code_class(@message.reason_code)}"}>
            <div class="card-body p-2">
              <code class="text-xs font-mono">{format_reason_code(@message.reason_code)}</code>
            </div>
          </div>
        </div>
      <% end %>
    </div>
    """
  end

  # Render CONNACK packet details with error highlighting
  defp render_connack_details(message) do
    assigns = %{message: message}

    ~H"""
    <div class="space-y-3">
      <!-- Connection Status -->
      <div class="space-y-2">
        <div class="flex items-center gap-2">
          <.icon name="hero-wifi" class="size-4 text-base-content/70" />
          <h4 class="text-sm font-medium text-base-content">Connection Status</h4>
        </div>
        <div class="alert alert-sm">
          <span class={get_ack_status_class(@message.reason_code)}>
            {format_connack_status(@message.reason_code)}
          </span>
        </div>
      </div>
      
    <!-- Session Information -->
      <div class="grid grid-cols-2 gap-3">
        <div class="stat stat-compact bg-base-200 rounded-lg">
          <div class="stat-title text-xs">Session Present</div>
          <div class="stat-value text-sm">
            <span class={"badge badge-sm #{if format_session_present(@message.extra_info) == "Yes", do: "badge-success", else: "badge-ghost"}"}>
              {format_session_present(@message.extra_info)}
            </span>
          </div>
        </div>

        <%= if @message.reason_code do %>
          <div class="stat stat-compact bg-base-200 rounded-lg">
            <div class="stat-title text-xs">Reason Code</div>
            <div class="stat-value text-xs font-mono">
              {format_reason_code(@message.reason_code)}
            </div>
          </div>
        <% end %>
      </div>
    </div>
    """
  end

  # Render CONNECT packet details
  defp render_connect_details(message, myself, payload_view_type) do
    assigns = %{message: message, myself: myself, payload_view_type: payload_view_type}

    ~H"""
    <div class="space-y-3">
      <%= if @message.extra_info && is_map(@message.extra_info) do %>
        <!-- Protocol Information -->
        <div class="space-y-2">
          <div class="flex items-center gap-2">
            <.icon name="hero-signal" class="size-4 text-base-content/70" />
            <h4 class="text-sm font-medium text-base-content">Protocol Information</h4>
          </div>
          <div class="grid grid-cols-3 gap-3">
            <div class="stat stat-compact bg-base-200 rounded-lg">
              <div class="stat-title text-xs">Protocol</div>
              <div class="stat-value text-sm">{@message.extra_info.proto_name || "N/A"}</div>
            </div>
            <div class="stat stat-compact bg-base-200 rounded-lg">
              <div class="stat-title text-xs">Version</div>
              <div class="stat-value text-sm">{@message.extra_info.proto_ver || "N/A"}</div>
            </div>
            <div class="stat stat-compact bg-base-200 rounded-lg">
              <div class="stat-title text-xs">Keep Alive</div>
              <div class="stat-value text-sm">{@message.extra_info.keepalive || "N/A"}s</div>
            </div>
          </div>
        </div>
        
    <!-- Client Information -->
        <div class="space-y-2">
          <div class="flex items-center gap-2">
            <.icon name="hero-user" class="size-4 text-base-content/70" />
            <h4 class="text-sm font-medium text-base-content">Client Information</h4>
          </div>
          <div class="space-y-2">
            <div class="bg-base-200 p-2 rounded-lg">
              <div class="text-xs text-base-content/70 mb-1">Client ID</div>
              <code class="text-sm font-mono text-base-content break-all">
                {@message.extra_info.clientid || "N/A"}
              </code>
            </div>
            <div class="grid grid-cols-2 gap-3">
              <div class="stat stat-compact bg-base-200 rounded-lg">
                <div class="stat-title text-xs">Clean Start</div>
                <div class="stat-value text-sm">
                  <span class={"badge badge-sm #{if @message.extra_info.clean_start, do: "badge-success", else: "badge-ghost"}"}>
                    {if @message.extra_info.clean_start, do: "Yes", else: "No"}
                  </span>
                </div>
              </div>
              <div class="stat stat-compact bg-base-200 rounded-lg">
                <div class="stat-title text-xs">Username</div>
                <div class="stat-value text-xs font-mono">
                  {@message.extra_info.username || "N/A"}
                </div>
              </div>
            </div>
          </div>
        </div>
        
    <!-- Will Message (if present) -->
        <%= if @message.extra_info.will_flag do %>
          <div class="space-y-2">
            <div class="flex items-center gap-2">
              <.icon name="hero-exclamation-triangle" class="size-4 text-base-content/70" />
              <h4 class="text-sm font-medium text-base-content">Will Message</h4>
            </div>
            <div class="card card-compact bg-base-200">
              <div class="card-body p-3">
                <div class="grid grid-cols-3 gap-3 mb-2">
                  <div class="text-xs">
                    <span class="text-base-content/70">Topic:</span>
                    <div class="font-mono text-base-content">
                      {@message.extra_info.will_topic || "N/A"}
                    </div>
                  </div>
                  <div class="text-xs">
                    <span class="text-base-content/70">QoS:</span>
                    <span class={"badge badge-xs #{qos_badge_class(@message.extra_info.will_qos)}"}>
                      {@message.extra_info.will_qos || "N/A"}
                    </span>
                  </div>
                  <div class="text-xs">
                    <span class="text-base-content/70">Retain:</span>
                    <span class={"badge badge-xs #{if @message.extra_info.will_retain, do: "badge-warning", else: "badge-ghost"}"}>
                      {if @message.extra_info.will_retain, do: "Yes", else: "No"}
                    </span>
                  </div>
                </div>

                <%= if @message.extra_info.will_payload do %>
                  <div class="space-y-2">
                    <div class="flex items-center justify-between">
                      <span class="text-xs text-base-content/70">Payload:</span>
                      <div class="tabs tabs-boxed tabs-xs">
                        <button
                          class={"tab tab-xs #{if @payload_view_type == "plaintext", do: "tab-active", else: ""}"}
                          phx-click="switch_payload_view"
                          phx-value-type="plaintext"
                          phx-target={@myself}
                        >
                          Text
                        </button>
                        <button
                          class={"tab tab-xs #{if @payload_view_type == "json", do: "tab-active", else: ""}"}
                          phx-click="switch_payload_view"
                          phx-value-type="json"
                          phx-target={@myself}
                        >
                          JSON
                        </button>
                        <button
                          class={"tab tab-xs #{if @payload_view_type == "hex", do: "tab-active", else: ""}"}
                          phx-click="switch_payload_view"
                          phx-value-type="hex"
                          phx-target={@myself}
                        >
                          Hex
                        </button>
                      </div>
                    </div>
                    <%= if @payload_view_type == "json" do %>
                      <div class="bg-base-300 rounded-lg">
                        <.render_json_viewer
                          payload={@message.extra_info.will_payload}
                          id={"will-payload-#{@message.id}"}
                        />
                      </div>
                    <% else %>
                      <div class="mockup-code bg-base-300">
                        <pre class="text-xs leading-relaxed px-4 py-2 max-h-24 overflow-y-auto"><code><%= format_payload_for_display(@message.extra_info.will_payload, @payload_view_type || "plaintext") %></code></pre>
                      </div>
                    <% end %>
                  </div>
                <% end %>
              </div>
            </div>
          </div>
        <% end %>
      <% end %>
    </div>
    """
  end

  # Render DISCONNECT packet details
  defp render_disconnect_details(message) do
    assigns = %{message: message}

    ~H"""
    <%= if @message.reason_code do %>
      <div class="space-y-2">
        <div class="flex items-center gap-2">
          <.icon name="hero-x-circle" class="size-4 text-base-content/70" />
          <h4 class="text-sm font-medium text-base-content">Disconnection Reason</h4>
        </div>
        <div class={"card card-compact #{get_reason_code_class(@message.reason_code)}"}>
          <div class="card-body p-2">
            <code class="text-xs font-mono">{format_reason_code(@message.reason_code)}</code>
          </div>
        </div>
      </div>
    <% else %>
      <div class="text-center text-base-content/60 py-4">
        <.icon name="hero-x-circle" class="size-8 mx-auto mb-2 opacity-50" />
        <p class="text-sm">Normal disconnection</p>
      </div>
    <% end %>
    """
  end

  # Render AUTH packet details
  defp render_auth_details(message) do
    assigns = %{message: message}

    ~H"""
    <%= if @message.reason_code do %>
      <div class="space-y-2">
        <div class="flex items-center gap-2">
          <.icon name="hero-shield-check" class="size-4 text-base-content/70" />
          <h4 class="text-sm font-medium text-base-content">Authentication Status</h4>
        </div>
        <div class={"card card-compact #{get_reason_code_class(@message.reason_code)}"}>
          <div class="card-body p-2">
            <code class="text-xs font-mono">{format_reason_code(@message.reason_code)}</code>
          </div>
        </div>
      </div>
    <% else %>
      <div class="text-center text-base-content/60 py-4">
        <.icon name="hero-shield-check" class="size-8 mx-auto mb-2 opacity-50" />
        <p class="text-sm">Authentication packet</p>
      </div>
    <% end %>
    """
  end

  # Render generic packet details for unknown types
  defp render_generic_details(message, myself, payload_view_type) do
    assigns = %{message: message, myself: myself, payload_view_type: payload_view_type}

    ~H"""
    <div class="space-y-3">
      <!-- Basic Information -->
      <%= if @message.packet_id do %>
        <div class="space-y-2">
          <div class="flex items-center gap-2">
            <.icon name="hero-hashtag" class="size-4 text-base-content/70" />
            <h4 class="text-sm font-medium text-base-content">Packet Information</h4>
          </div>
          <div class="stat stat-compact bg-base-200 rounded-lg">
            <div class="stat-title text-xs">Packet ID</div>
            <div class="stat-value text-sm font-mono">{@message.packet_id}</div>
          </div>
        </div>
      <% end %>
      
    <!-- Payload Section -->
      <%= if @message.payload_length && @message.payload_length > 0 do %>
        <div class="space-y-2">
          <div class="flex items-center justify-between">
            <div class="flex items-center gap-2">
              <.icon name="hero-document-text" class="size-4 text-base-content/70" />
              <h4 class="text-sm font-medium text-base-content">Payload</h4>
            </div>
            <div class="tabs tabs-boxed tabs-xs">
              <button
                class={"tab tab-xs #{if @payload_view_type == "plaintext", do: "tab-active", else: ""}"}
                phx-click="switch_payload_view"
                phx-value-type="plaintext"
                phx-target={@myself}
              >
                Text
              </button>
              <button
                class={"tab tab-xs #{if @payload_view_type == "json", do: "tab-active", else: ""}"}
                phx-click="switch_payload_view"
                phx-value-type="json"
                phx-target={@myself}
              >
                JSON
              </button>
              <button
                class={"tab tab-xs #{if @payload_view_type == "hex", do: "tab-active", else: ""}"}
                phx-click="switch_payload_view"
                phx-value-type="hex"
                phx-target={@myself}
              >
                Hex
              </button>
            </div>
          </div>
          <%= if @payload_view_type == "json" do %>
            <div class="bg-base-200 rounded-lg">
              <.render_json_viewer
                payload={@message.payload || ""}
                id={"generic-payload-#{@message.id}"}
              />
            </div>
          <% else %>
            <div class="mockup-code bg-base-200">
              <pre class="text-xs leading-relaxed px-4 py-2 max-h-32 overflow-y-auto"><code><%= format_payload_for_display(@message.payload || "", @payload_view_type || "plaintext") %></code></pre>
            </div>
          <% end %>
        </div>
      <% else %>
        <div class="text-center text-base-content/60 py-4">
          <.icon name="hero-document" class="size-8 mx-auto mb-2 opacity-50" />
          <p class="text-sm">No additional packet details available</p>
        </div>
      <% end %>
    </div>
    """
  end

  # Helper functions for error highlighting and status formatting

  # Get CSS class for ACK status (success/error highlighting)
  defp get_ack_status_class(reason_code) when is_integer(reason_code) do
    if reason_code == 0 do
      "text-xs badge badge-success"
    else
      "text-xs badge badge-error"
    end
  end

  defp get_ack_status_class(reason_codes) when is_list(reason_codes) do
    if Enum.all?(reason_codes, &(&1 == 0)) do
      "text-xs badge badge-success"
    else
      "text-xs badge badge-error"
    end
  end

  defp get_ack_status_class(_), do: "text-xs badge badge-ghost"

  # Get CSS class for individual reason codes
  defp get_reason_code_class(reason_code) when is_integer(reason_code) do
    if reason_code == 0 do
      "bg-success/20 text-success"
    else
      "bg-error/20 text-error"
    end
  end

  defp get_reason_code_class(_), do: "bg-base-200 text-base-content"

  # Format SUBACK status
  defp format_suback_status(reason_codes) when is_list(reason_codes) do
    success_count = Enum.count(reason_codes, &(&1 == 0))
    total_count = length(reason_codes)

    if success_count == total_count do
      "All Subscriptions Successful"
    else
      "#{success_count}/#{total_count} Subscriptions Successful"
    end
  end

  defp format_suback_status(_), do: "Unknown"

  # Format UNSUBACK status
  defp format_unsuback_status(reason_codes) when is_list(reason_codes) do
    success_count = Enum.count(reason_codes, &(&1 == 0))
    total_count = length(reason_codes)

    if success_count == total_count do
      "All Unsubscriptions Successful"
    else
      "#{success_count}/#{total_count} Unsubscriptions Successful"
    end
  end

  defp format_unsuback_status(_), do: "Unknown"

  # Format PUBACK/PUBREC/PUBREL/PUBCOMP status
  defp format_pub_ack_status(reason_code) when is_integer(reason_code) do
    case reason_code do
      0 -> "Success"
      16 -> "No matching subscribers"
      128 -> "Unspecified error"
      129 -> "Implementation specific error"
      135 -> "Not authorized"
      144 -> "Topic Name invalid"
      145 -> "Packet identifier in use"
      146 -> "Packet identifier not found"
      147 -> "Receive Maximum exceeded"
      148 -> "Topic Alias invalid"
      149 -> "Packet too large"
      150 -> "Message rate too high"
      151 -> "Quota exceeded"
      152 -> "Administrative action"
      153 -> "Payload format invalid"
      154 -> "Retain not supported"
      155 -> "QoS not supported"
      156 -> "Use another server"
      157 -> "Server moved"
      158 -> "Shared Subscriptions not supported"
      159 -> "Connection rate exceeded"
      160 -> "Maximum connect time"
      161 -> "Subscription Identifiers not supported"
      162 -> "Wildcard Subscriptions not supported"
      _ -> "Unknown (#{reason_code})"
    end
  end

  defp format_pub_ack_status(_), do: "Unknown"

  # Format CONNACK status
  defp format_connack_status(reason_code) when is_integer(reason_code) do
    case reason_code do
      0 -> "Connection Accepted"
      128 -> "Unspecified error"
      129 -> "Malformed Packet"
      130 -> "Protocol Error"
      131 -> "Implementation specific error"
      132 -> "Unsupported Protocol Version"
      133 -> "Client Identifier not valid"
      134 -> "Bad User Name or Password"
      135 -> "Not authorized"
      136 -> "Server unavailable"
      137 -> "Server busy"
      138 -> "Banned"
      140 -> "Bad authentication method"
      144 -> "Topic Name invalid"
      149 -> "Packet too large"
      151 -> "Quota exceeded"
      153 -> "Payload format invalid"
      154 -> "Retain not supported"
      155 -> "QoS not supported"
      156 -> "Use another server"
      157 -> "Server moved"
      159 -> "Connection rate exceeded"
      _ -> "Unknown (#{reason_code})"
    end
  end

  defp format_connack_status(_), do: "Unknown"

  # Format reason codes with descriptive text
  defp format_reason_code(reason_code) when is_integer(reason_code) do
    case reason_code do
      0 -> "0x00 - Success"
      1 -> "0x01 - Normal disconnection"
      2 -> "0x02 - Granted QoS 0"
      4 -> "0x04 - Disconnect with Will Message"
      16 -> "0x10 - No matching subscribers"
      17 -> "0x11 - No subscription existed"
      24 -> "0x18 - Continue authentication"
      25 -> "0x19 - Re-authenticate"
      128 -> "0x80 - Unspecified error"
      129 -> "0x81 - Malformed Packet"
      130 -> "0x82 - Protocol Error"
      131 -> "0x83 - Implementation specific error"
      132 -> "0x84 - Unsupported Protocol Version"
      133 -> "0x85 - Client Identifier not valid"
      134 -> "0x86 - Bad User Name or Password"
      135 -> "0x87 - Not authorized"
      136 -> "0x88 - Server unavailable"
      137 -> "0x89 - Server busy"
      138 -> "0x8A - Banned"
      139 -> "0x8B - Server shutting down"
      140 -> "0x8C - Bad authentication method"
      141 -> "0x8D - Keep Alive timeout"
      142 -> "0x8E - Session taken over"
      143 -> "0x8F - Topic Filter invalid"
      144 -> "0x90 - Topic Name invalid"
      145 -> "0x91 - Packet identifier in use"
      146 -> "0x92 - Packet identifier not found"
      147 -> "0x93 - Receive Maximum exceeded"
      148 -> "0x94 - Topic Alias invalid"
      149 -> "0x95 - Packet too large"
      150 -> "0x96 - Message rate too high"
      151 -> "0x97 - Quota exceeded"
      152 -> "0x98 - Administrative action"
      153 -> "0x99 - Payload format invalid"
      154 -> "0x9A - Retain not supported"
      155 -> "0x9B - QoS not supported"
      156 -> "0x9C - Use another server"
      157 -> "0x9D - Server moved"
      158 -> "0x9E - Shared Subscriptions not supported"
      159 -> "0x9F - Connection rate exceeded"
      160 -> "0xA0 - Maximum connect time"
      161 -> "0xA1 - Subscription Identifiers not supported"
      162 -> "0xA2 - Wildcard Subscriptions not supported"
      _ -> "0x#{Integer.to_string(reason_code, 16) |> String.upcase()} - Unknown"
    end
  end

  defp format_reason_code(reason_code), do: "#{inspect(reason_code)} - Unknown"

  # Format session present flag from CONNACK
  defp format_session_present(ack_flags) when is_integer(ack_flags) do
    if (ack_flags &&& 1) == 1, do: "Yes", else: "No"
  end

  defp format_session_present(_), do: "N/A"

  # Format payload for different display types
  defp format_payload_for_display(payload, view_type) when is_binary(payload) do
    case view_type do
      "json" ->
        # For JSON view type, we'll handle this in the template with the JSON viewer component
        payload

      "hex" ->
        format_payload_as_hex(payload)

      _ ->
        # Default to plaintext
        payload
    end
  end

  defp format_payload_for_display(payload, _view_type), do: inspect(payload)

  # Check if payload is valid JSON
  defp is_valid_json?(payload) when is_binary(payload) do
    case Jason.decode(payload) do
      {:ok, _} -> true
      {:error, _} -> false
    end
  end

  defp is_valid_json?(_), do: false

  # Render JSON viewer component
  defp render_json_viewer(assigns) do
    ~H"""
    <%= if is_valid_json?(@payload) do %>
      <div
        id={"json-viewer-#{@id}"}
        class="json-viewer-container"
        phx-hook="JsonViewer"
        data-json={@payload}
      >
        <!-- JSON viewer will be rendered here by the hook -->
      </div>
    <% else %>
      <div class="json-viewer-error">
        <div class="json-error-header">Invalid JSON Data</div>
        <pre class="json-raw-content"><%= @payload %></pre>
      </div>
    <% end %>
    """
  end

  # Format payload as JSON with pretty printing (legacy function, kept for compatibility)
  defp format_payload_as_json(payload) do
    case Jason.decode(payload) do
      {:ok, decoded} ->
        case Jason.encode(decoded, pretty: true) do
          {:ok, pretty_json} -> pretty_json
          {:error, _} -> payload
        end

      {:error, _} ->
        # If it's not valid JSON, return original payload with a note
        "# Not valid JSON\n#{payload}"
    end
  end

  # Format payload as hexadecimal
  defp format_payload_as_hex(payload) do
    payload
    |> :binary.bin_to_list()
    |> Enum.chunk_every(16)
    |> Enum.with_index()
    |> Enum.map(fn {chunk, index} ->
      offset = String.pad_leading(Integer.to_string(index * 16, 16), 8, "0")

      hex_part =
        chunk
        |> Enum.map(&String.pad_leading(Integer.to_string(&1, 16), 2, "0"))
        |> Enum.join(" ")
        |> String.pad_trailing(47, " ")

      ascii_part =
        chunk
        |> Enum.map(fn byte ->
          if byte >= 32 and byte <= 126, do: <<byte>>, else: "."
        end)
        |> Enum.join("")

      "#{offset}  #{hex_part}  |#{ascii_part}|"
    end)
    |> Enum.join("\n")
  end
end
